@import "tailwindcss";

/* 基础样式重置和字体设置 */
* {
  box-sizing: border-box;
}

html {
  /* 基础字体大小，配合 rem 适配 */
  font-size: 37.5px; /* 1rem = 37.5px，基于 750px 设计稿 */
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  font-size: 0.373rem; /* 14px */
  color: #333;
  background-color: #fff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 移动端适配 */
@media screen and (max-width: 320px) {
  html {
    font-size: 16px; /* 小屏幕设备使用较小的基准字体 */
  }
}

@media screen and (min-width: 750px) {
  html {
    font-size: 37.5px; /* 大屏幕保持标准大小 */
  }
}
