<template>
  <div class="player-view">
    <div class="player-container">
      <h1>播放器</h1>
      <p>这是全屏播放器页面</p>
      <p>ID: {{ $route.params.id }}</p>
      <div class="player-controls">
        <button @click="toggleFullscreen">切换全屏</button>
        <button @click="goBack">返回</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const toggleFullscreen = () => {
  if (document.fullscreenElement) {
    document.exitFullscreen()
  } else {
    document.documentElement.requestFullscreen()
  }
}

const goBack = () => {
  router.back()
}
</script>

<style scoped>
.player-view {
  width: 100%;
  height: 100%;
  background-color: #000;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.player-container {
  text-align: center;
}

.player-controls {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  justify-content: center;
}

.player-controls button {
  padding: 10px 20px;
  background-color: #0052d9;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.player-controls button:hover {
  background-color: #0041a8;
}
</style>
