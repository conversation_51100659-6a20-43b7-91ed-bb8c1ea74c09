<template>
  <div class="full-layout">
    <!-- 全屏内容区域 -->
    <div class="layout-content" ref="contentRef">
      <slot>
        <RouterView />
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { RouterView } from 'vue-router'

const contentRef = ref<HTMLElement>()

// 处理窗口大小变化
const handleResize = () => {
  if (contentRef.value) {
    contentRef.value.style.height = 'auto'
    setTimeout(() => {
      if (contentRef.value) {
        contentRef.value.style.height = '100vh'
      }
    }, 0)
  }
}

// 进入全屏模式
const enterFullscreen = () => {
  if (document.documentElement.requestFullscreen) {
    document.documentElement.requestFullscreen()
  }
}

// 退出全屏模式
const exitFullscreen = () => {
  if (document.exitFullscreen) {
    document.exitFullscreen()
  }
}

// 切换全屏模式
const toggleFullscreen = () => {
  if (document.fullscreenElement) {
    exitFullscreen()
  } else {
    enterFullscreen()
  }
}

// 暴露方法给父组件使用
defineExpose({
  enterFullscreen,
  exitFullscreen,
  toggleFullscreen
})

onMounted(() => {
  window.addEventListener('resize', handleResize)
  
  // 监听全屏状态变化
  document.addEventListener('fullscreenchange', () => {
    // 全屏状态变化时的处理逻辑
    if (document.fullscreenElement) {
      // 进入全屏
      document.body.style.overflow = 'hidden'
    } else {
      // 退出全屏
      document.body.style.overflow = ''
    }
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  document.body.style.overflow = ''
})
</script>

<script lang="ts">
export default {
  name: 'FullLayout'
}
</script>

<style scoped>
.full-layout {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background-color: #000;
  overflow: hidden;
  z-index: 9999;
}

.layout-content {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

/* 全屏模式下的样式 */
:fullscreen .full-layout,
:-webkit-full-screen .full-layout,
:-moz-full-screen .full-layout {
  width: 100vw;
  height: 100vh;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .full-layout {
    /* 移动端全屏时隐藏地址栏 */
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
  }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-width: 768px) {
  .full-layout {
    height: 100vh;
  }
}

/* 防止内容溢出 */
.layout-content > * {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* 全屏过渡动画 */
.full-layout {
  transition: all 0.3s ease;
}

/* 支持安全区域 */
@supports (padding: env(safe-area-inset-top)) {
  .layout-content {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}
</style>
