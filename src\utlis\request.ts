import axios, {
  type AxiosInstance,
  type AxiosRequestConfig,
  type AxiosResponse,
  type AxiosError,
} from 'axios'

// 定义响应数据的通用接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 定义请求配置接口
export interface RequestConfig extends AxiosRequestConfig {
  showLoading?: boolean
  showError?: boolean
}

class RequestService {
  private instance: AxiosInstance
  private loadingCount = 0

  constructor() {
    // 创建axios实例
    this.instance = axios.create({
      baseURL: '/api', // 使用代理前缀
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // 设置请求拦截器
    this.setupRequestInterceptors()
    // 设置响应拦截器
    this.setupResponseInterceptors()
  }

  /**
   * 设置请求拦截器
   */
  private setupRequestInterceptors() {
    this.instance.interceptors.request.use(
      (config: any) => {
        // 显示loading
        if (config.showLoading !== false) {
          this.showLoading()
        }

        // 添加token
        const token = this.getToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }

        // 添加时间戳防止缓存
        if (config.method === 'get') {
          config.params = {
            ...config.params,
            _t: Date.now(),
          }
        }

        console.log('请求发送:', config)
        return config
      },
      (error: AxiosError) => {
        this.hideLoading()
        console.error('请求错误:', error)
        return Promise.reject(error)
      },
    )
  }

  /**
   * 设置响应拦截器
   */
  private setupResponseInterceptors() {
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        this.hideLoading()

        const { data } = response
        console.log('响应接收:', data)

        // 根据业务状态码处理
        if (data.code === 200 || data.success) {
          return data
        } else if (data.code === 401) {
          // token过期，跳转登录
          this.handleTokenExpired()
          return Promise.reject(new Error(data.message || '登录已过期'))
        } else {
          // 其他业务错误
          const error = new Error(data.message || '请求失败')
          if (response.config.showError !== false) {
            this.showError(error.message)
          }
          return Promise.reject(error)
        }
      },
      (error: AxiosError) => {
        this.hideLoading()
        console.error('响应错误:', error)

        let message = '网络错误'
        if (error.response) {
          const status = error.response.status
          switch (status) {
            case 400:
              message = '请求参数错误'
              break
            case 401:
              message = '未授权，请重新登录'
              this.handleTokenExpired()
              break
            case 403:
              message = '拒绝访问'
              break
            case 404:
              message = '请求地址不存在'
              break
            case 500:
              message = '服务器内部错误'
              break
            case 502:
              message = '网关错误'
              break
            case 503:
              message = '服务不可用'
              break
            case 504:
              message = '网关超时'
              break
            default:
              message = `连接错误${status}`
          }
        } else if (error.code === 'ECONNABORTED') {
          message = '请求超时'
        } else if (error.message.includes('Network Error')) {
          message = '网络连接异常'
        }

        if (error.config?.showError !== false) {
          this.showError(message)
        }

        return Promise.reject(new Error(message))
      },
    )
  }

  /**
   * 获取token
   */
  private getToken(): string | null {
    return localStorage.getItem('token') || sessionStorage.getItem('token')
  }

  /**
   * 处理token过期
   */
  private handleTokenExpired() {
    localStorage.removeItem('token')
    sessionStorage.removeItem('token')
    // 这里可以跳转到登录页面
    // window.location.href = '/login'
  }

  /**
   * 显示loading
   */
  private showLoading() {
    this.loadingCount++
    // 这里可以集成UI库的loading组件
    console.log('显示loading')
  }

  /**
   * 隐藏loading
   */
  private hideLoading() {
    this.loadingCount--
    if (this.loadingCount <= 0) {
      this.loadingCount = 0
      // 这里可以集成UI库的loading组件
      console.log('隐藏loading')
    }
  }

  /**
   * 显示错误信息
   */
  private showError(message: string) {
    // 这里可以集成UI库的消息提示组件
    console.error('错误提示:', message)
  }

  /**
   * GET请求
   */
  get<T = any>(url: string, params?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.instance.get(url, { params, ...config })
  }

  /**
   * POST请求
   */
  post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.instance.post(url, data, config)
  }

  /**
   * PUT请求
   */
  put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.instance.put(url, data, config)
  }

  /**
   * DELETE请求
   */
  delete<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.instance.delete(url, config)
  }

  /**
   * 上传文件
   */
  upload<T = any>(url: string, file: File, config?: RequestConfig): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)

    return this.instance.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      ...config,
    })
  }

  /**
   * 下载文件
   */
  download(url: string, params?: any, filename?: string): Promise<void> {
    return this.instance
      .get(url, {
        params,
        responseType: 'blob',
      })
      .then((response: any) => {
        const blob = new Blob([response.data])
        const downloadUrl = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = filename || 'download'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(downloadUrl)
      })
  }
}

// 创建请求实例
const request = new RequestService()

export default request
