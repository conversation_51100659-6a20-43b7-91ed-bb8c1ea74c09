<template>
  <div class="login-view">
    <h1>登录</h1>
    <p>这是登录页面，使用空白布局（无导航栏）</p>
    <div class="login-form">
      <input type="text" placeholder="用户名" />
      <input type="password" placeholder="密码" />
      <button>登录</button>
    </div>
  </div>
</template>

<script setup lang="ts">
// 登录页面逻辑
</script>

<style scoped>
.login-view {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  max-width: 300px;
}

.login-form input,
.login-form button {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.login-form button {
  background-color: #0052d9;
  color: white;
  border: none;
  cursor: pointer;
}
</style>
