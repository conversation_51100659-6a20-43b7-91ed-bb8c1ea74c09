<template>
  <div class="blank-layout">
    <div class="layout-content">
      <slot>
        <RouterView />
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { RouterView } from 'vue-router'
</script>

<script lang="ts">
export default {
  name: 'BlankLayout'
}
</script>

<style scoped>
.blank-layout {
  width: 100vw;
  height: 100vh;
  background-color: #fff;
  overflow: hidden;
}

.layout-content {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* 响应式适配 */
@media (min-width: 768px) {
  .blank-layout {
    max-width: 414px;
    margin: 0 auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  }
}

/* 滚动条样式 */
.layout-content::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

/* 内容区域动画 */
.layout-content {
  transition: all 0.3s ease;
}

/* 支持安全区域 */
@supports (padding: env(safe-area-inset-top)) {
  .layout-content {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}
</style>
