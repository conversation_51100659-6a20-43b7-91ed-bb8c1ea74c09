import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

// 路由元信息类型定义
declare module 'vue-router' {
  interface RouteMeta {
    title?: string
    layout?: 'default' | 'blank' | 'navbar' | 'tabbar' | 'full'
    showBack?: boolean
    requiresAuth?: boolean
    keepAlive?: boolean
  }
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: {
        title: '首页',
        layout: 'default',
      },
    },
    {
      path: '/category',
      name: 'category',
      component: () => import('../views/CategoryView.vue'),
      meta: {
        title: '分类',
        layout: 'default',
      },
    },
    {
      path: '/cart',
      name: 'cart',
      component: () => import('../views/CartView.vue'),
      meta: {
        title: '购物车',
        layout: 'default',
      },
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
      meta: {
        title: '我的',
        layout: 'default',
      },
    },
    {
      path: '/gold/charts',
      name: 'charts',
      component: () => import('../views/gold/charts.vue'),
      meta: {
        title: '黄金统计',
        layout: 'blank',
        showBack: true,
      },
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
      meta: {
        title: '关于',
        layout: 'blank',
        showBack: true,
      },
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: {
        title: '登录',
        layout: 'blank',
      },
    },
    {
      path: '/detail/:id',
      name: 'detail',
      component: () => import('../views/DetailView.vue'),
      meta: {
        title: '详情',
        layout: 'navbar',
        showBack: true,
      },
    },
    {
      path: '/player/:id',
      name: 'player',
      component: () => import('../views/PlayerView.vue'),
      meta: {
        title: '播放器',
        layout: 'full',
      },
    },
    {
      path: '/api-test',
      name: 'api-test',
      component: () => import('../views/ApiTestView.vue'),
      meta: {
        title: 'API测试',
        layout: 'blank',
        showBack: true,
      },
    },
  ],
})

export default router
