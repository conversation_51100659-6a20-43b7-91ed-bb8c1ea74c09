/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    IconCommunity: typeof import('./src/components/icons/IconCommunity.vue')['default']
    IconDocumentation: typeof import('./src/components/icons/IconDocumentation.vue')['default']
    IconEcosystem: typeof import('./src/components/icons/IconEcosystem.vue')['default']
    IconSupport: typeof import('./src/components/icons/IconSupport.vue')['default']
    IconTooling: typeof import('./src/components/icons/IconTooling.vue')['default']
    MobileLayout: typeof import('./src/components/MobileLayout.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TButton: typeof import('tdesign-mobile-vue')['Button']
    TConfigProvider: typeof import('tdesign-mobile-vue')['ConfigProvider']
    TIcon: typeof import('tdesign-mobile-vue')['Icon']
    TNavbar: typeof import('tdesign-mobile-vue')['Navbar']
    TSwiper: typeof import('tdesign-mobile-vue')['Swiper']
    TSwiperItem: typeof import('tdesign-mobile-vue')['SwiperItem']
    TTabBar: typeof import('tdesign-mobile-vue')['TabBar']
    TTabBarItem: typeof import('tdesign-mobile-vue')['TabBarItem']
  }
}
