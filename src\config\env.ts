// 环境配置
export interface EnvConfig {
  apiBaseUrl: string
  uploadUrl: string
  wsUrl?: string
  isDev: boolean
  isProd: boolean
}

// 获取当前环境
const getEnv = (): string => {
  return import.meta.env.MODE || 'development'
}

// 开发环境配置
const devConfig: EnvConfig = {
  apiBaseUrl: '/api', // 使用代理
  uploadUrl: '/upload',
  wsUrl: 'ws://localhost:8080/ws',
  isDev: true,
  isProd: false,
}

// 生产环境配置
const prodConfig: EnvConfig = {
  apiBaseUrl: 'https://api.yourapp.com', // 生产环境API地址
  uploadUrl: 'https://api.yourapp.com/upload',
  wsUrl: 'wss://api.yourapp.com/ws',
  isDev: false,
  isProd: true,
}

// 测试环境配置
const testConfig: EnvConfig = {
  apiBaseUrl: 'https://test-api.yourapp.com',
  uploadUrl: 'https://test-api.yourapp.com/upload',
  wsUrl: 'wss://test-api.yourapp.com/ws',
  isDev: false,
  isProd: false,
}

// 根据环境获取配置
const getConfig = (): EnvConfig => {
  const env = getEnv()
  
  switch (env) {
    case 'development':
      return devConfig
    case 'production':
      return prodConfig
    case 'test':
      return testConfig
    default:
      return devConfig
  }
}

export const config = getConfig()

// 导出常用配置
export const { apiBaseUrl, uploadUrl, wsUrl, isDev, isProd } = config

export default config
