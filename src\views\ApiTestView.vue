<template>
  <div class="api-test-page">
    <div class="header">
      <h1>API测试页面</h1>
      <p>测试axios请求工具和反向代理配置</p>
    </div>

    <div class="test-section">
      <h2>基础请求测试</h2>
      <div class="button-group">
        <button @click="testGet" class="btn btn-primary">测试GET请求</button>
        <button @click="testPost" class="btn btn-success">测试POST请求</button>
        <button @click="testError" class="btn btn-warning">测试错误处理</button>
      </div>
    </div>

    <div class="test-section">
      <h2>API模块测试</h2>
      <div class="button-group">
        <button @click="testUserApi" class="btn btn-info">测试用户API</button>
        <button @click="testProductApi" class="btn btn-secondary">测试商品API</button>
        <button @click="testChartApi" class="btn btn-dark">测试图表API</button>
      </div>
    </div>

    <div class="test-section">
      <h2>文件上传测试</h2>
      <input 
        type="file" 
        @change="testUpload" 
        accept="image/*"
        class="file-input"
      >
    </div>

    <div class="result-section" v-if="result">
      <h2>请求结果</h2>
      <div class="result-box">
        <div class="result-status" :class="result.success ? 'success' : 'error'">
          {{ result.success ? '✅ 成功' : '❌ 失败' }}
        </div>
        <div class="result-content">
          <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
        </div>
      </div>
    </div>

    <div class="info-section">
      <h2>配置信息</h2>
      <div class="info-box">
        <p><strong>环境:</strong> {{ config.isDev ? '开发环境' : '生产环境' }}</p>
        <p><strong>API地址:</strong> {{ config.apiBaseUrl }}</p>
        <p><strong>上传地址:</strong> {{ config.uploadUrl }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import request from '@/utlis/request'
import api from '@/utlis/api'
import config from '@/config/env'

const result = ref<any>(null)

// 测试GET请求
const testGet = async () => {
  try {
    const response = await request.get('/test/get', { message: 'Hello World' })
    result.value = { success: true, data: response }
  } catch (error: any) {
    result.value = { success: false, data: { error: error.message } }
  }
}

// 测试POST请求
const testPost = async () => {
  try {
    const response = await request.post('/test/post', {
      name: 'Test User',
      email: '<EMAIL>'
    })
    result.value = { success: true, data: response }
  } catch (error: any) {
    result.value = { success: false, data: { error: error.message } }
  }
}

// 测试错误处理
const testError = async () => {
  try {
    const response = await request.get('/test/error')
    result.value = { success: true, data: response }
  } catch (error: any) {
    result.value = { success: false, data: { error: error.message } }
  }
}

// 测试用户API
const testUserApi = async () => {
  try {
    const response = await api.user.getUserInfo()
    result.value = { success: true, data: response }
  } catch (error: any) {
    result.value = { success: false, data: { error: error.message } }
  }
}

// 测试商品API
const testProductApi = async () => {
  try {
    const response = await api.product.getProductList({ page: 1, pageSize: 5 })
    result.value = { success: true, data: response }
  } catch (error: any) {
    result.value = { success: false, data: { error: error.message } }
  }
}

// 测试图表API
const testChartApi = async () => {
  try {
    const response = await api.chart.getSalesData('2024')
    result.value = { success: true, data: response }
  } catch (error: any) {
    result.value = { success: false, data: { error: error.message } }
  }
}

// 测试文件上传
const testUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (!file) return
  
  try {
    const response = await api.upload.uploadImage(file)
    result.value = { success: true, data: response }
  } catch (error: any) {
    result.value = { success: false, data: { error: error.message } }
  }
}
</script>

<style scoped>
.api-test-page {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 40px;
}

.header h1 {
  color: #333;
  margin-bottom: 10px;
}

.header p {
  color: #666;
  font-size: 16px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  background: #f8f9fa;
}

.test-section h2 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #495057;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-primary { background: #007bff; color: white; }
.btn-success { background: #28a745; color: white; }
.btn-warning { background: #ffc107; color: #212529; }
.btn-info { background: #17a2b8; color: white; }
.btn-secondary { background: #6c757d; color: white; }
.btn-dark { background: #343a40; color: white; }

.file-input {
  padding: 10px;
  border: 2px dashed #dee2e6;
  border-radius: 6px;
  background: white;
  width: 100%;
  cursor: pointer;
}

.result-section {
  margin-top: 30px;
}

.result-box {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
}

.result-status {
  padding: 15px;
  font-weight: bold;
  font-size: 16px;
}

.result-status.success {
  background: #d4edda;
  color: #155724;
  border-bottom: 1px solid #c3e6cb;
}

.result-status.error {
  background: #f8d7da;
  color: #721c24;
  border-bottom: 1px solid #f5c6cb;
}

.result-content {
  padding: 15px;
  background: white;
}

.result-content pre {
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  color: #495057;
}

.info-section {
  margin-top: 30px;
}

.info-box {
  padding: 20px;
  background: #e9ecef;
  border-radius: 8px;
}

.info-box p {
  margin: 5px 0;
  color: #495057;
}

.info-box strong {
  color: #212529;
}
</style>
