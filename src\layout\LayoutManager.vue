<template>
  <component :is="currentLayout">
    <RouterView />
  </component>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import DefaultLayout from './default.vue'
import BlankLayout from './blank.vue'
import NavbarLayout from './navbar.vue'
import TabbarLayout from './tabbar.vue'
import FullLayout from './full.vue'

// 布局组件映射
const layoutComponents = {
  default: DefaultLayout,
  blank: BlankLayout,
  navbar: NavbarLayout,
  tabbar: TabbarLayout,
  full: FullLayout
}

const route = useRoute()

// 根据路由元信息或视图命名自动选择布局
const currentLayout = computed(() => {
  // 1. 优先使用路由元信息中的布局配置
  if (route.meta?.layout && layoutComponents[route.meta.layout as keyof typeof layoutComponents]) {
    return layoutComponents[route.meta.layout as keyof typeof layoutComponents]
  }

  // 2. 根据视图命名规则自动判断布局
  const routeName = route.name as string
  const routePath = route.path

  // 根据路由名称判断布局类型
  if (routeName) {
    // 登录、注册等页面使用空白布局
    if (routeName.includes('login') || routeName.includes('register') || routeName.includes('auth')) {
      return layoutComponents.blank
    }

    // 详情页、编辑页等使用仅顶部导航布局
    if (routeName.includes('detail') || routeName.includes('edit') || routeName.includes('form')) {
      return layoutComponents.navbar
    }

    // 全屏页面（如播放器、预览等）
    if (routeName.includes('player') || routeName.includes('preview') || routeName.includes('fullscreen')) {
      return layoutComponents.full
    }

    // 主要页面使用完整布局（顶部+底部导航）
    if (routeName.includes('home') || routeName.includes('list') || routeName.includes('category') ||
        routeName.includes('profile') || routeName.includes('mine') || routeName.includes('user')) {
      return layoutComponents.default
    }
  }

  // 3. 根据路径判断布局类型
  if (routePath) {
    // 根路径和主要页面路径使用完整布局
    if (routePath === '/' || routePath === '/home' || routePath === '/category' ||
        routePath === '/profile' || routePath === '/mine') {
      return layoutComponents.default
    }

    // 二级页面使用仅顶部导航布局
    if (routePath.split('/').length > 2) {
      return layoutComponents.navbar
    }
  }

  // 4. 默认使用完整布局
  return layoutComponents.default
})
</script>

<script lang="ts">
export default {
  name: 'LayoutManager'
}
</script>
