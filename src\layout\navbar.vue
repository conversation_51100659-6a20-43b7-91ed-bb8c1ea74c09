<template>
  <div class="navbar-layout">
    <!-- 顶部导航栏 -->
    <t-navbar 
      :title="navbarTitle" 
      :fixed="true" 
      :left-arrow="showBackButton" 
      @left-click="handleBack"
      class="layout-navbar"
    >
      <template #right>
        <slot name="navbar-right"></slot>
      </template>
    </t-navbar>
    
    <!-- 主内容区域 -->
    <div class="layout-main" ref="mainRef">
      <div class="layout-content">
        <slot>
          <RouterView />
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { RouterView } from 'vue-router'

const route = useRoute()
const router = useRouter()
const mainRef = ref<HTMLElement>()

// 导航栏标题
const navbarTitle = computed(() => {
  return route.meta?.title as string || '详情'
})

// 是否显示返回按钮
const showBackButton = computed(() => {
  return route.meta?.showBack !== false
})

// 处理返回按钮点击
const handleBack = () => {
  const position = router.options.history.state?.position
  if (typeof position === 'number' && position > 0) {
    router.back()
  } else {
    router.push('/')
  }
}

// 处理窗口大小变化
const handleResize = () => {
  if (mainRef.value) {
    mainRef.value.style.height = 'auto'
    setTimeout(() => {
      if (mainRef.value) {
        mainRef.value.style.height = ''
      }
    }, 0)
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<script lang="ts">
export default {
  name: 'NavbarLayout'
}
</script>

<style scoped>
.navbar-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  overflow: hidden;
}

.layout-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
  border-bottom: 1px solid #e7e7e7;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.layout-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-top: 48px; /* navbar height */
  overflow: hidden;
}

.layout-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* 安全区域适配 */
@supports (padding-top: env(safe-area-inset-top)) {
  .layout-navbar {
    padding-top: env(safe-area-inset-top);
  }
  
  .layout-main {
    padding-top: calc(48px + env(safe-area-inset-top));
  }
}

/* 响应式适配 */
@media (max-width: 320px) {
  .layout-main {
    padding-top: 44px;
  }
}

@media (min-width: 768px) {
  .navbar-layout {
    max-width: 414px;
    margin: 0 auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  }
}

/* 滚动条样式 */
.layout-content::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

/* 内容区域动画 */
.layout-content {
  transition: all 0.3s ease;
}

/* 防止内容被导航栏遮挡 */
.layout-content > * {
  position: relative;
  z-index: 1;
}
</style>
