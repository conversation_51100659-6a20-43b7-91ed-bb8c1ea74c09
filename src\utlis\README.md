# 请求工具使用说明

## 概述

本项目已集成axios并封装了完整的请求工具，包含以下功能：

- 🚀 基于axios的请求封装
- 🔄 请求/响应拦截器
- 🔐 自动token管理
- 📱 Loading状态管理
- ⚠️ 错误处理和提示
- 🔧 TypeScript类型支持
- 🌐 反向代理配置

## 文件结构

```
src/utlis/
├── request.ts      # 核心请求工具
├── api.ts          # API接口定义
├── example.vue     # 使用示例
└── README.md       # 说明文档

src/config/
└── env.ts          # 环境配置
```

## 快速开始

### 1. 基础使用

```typescript
import request from '@/utlis/request'

// GET请求
const response = await request.get('/user/info')

// POST请求
const response = await request.post('/user/login', {
  username: 'admin',
  password: '123456'
})
```

### 2. 使用API模块

```typescript
import api from '@/utlis/api'

// 用户登录
const loginResponse = await api.user.login({
  username: 'admin',
  password: '123456'
})

// 获取商品列表
const productsResponse = await api.product.getProductList({
  page: 1,
  pageSize: 10
})
```

### 3. 在Vue组件中使用

```vue
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import api from '@/utlis/api'

const userInfo = ref(null)

onMounted(async () => {
  try {
    const response = await api.user.getUserInfo()
    userInfo.value = response.data
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
})
</script>
```

## 配置说明

### 反向代理配置

在 `vite.config.ts` 中已配置反向代理：

```typescript
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8080', // 后端服务地址
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '')
    }
  }
}
```

### 环境配置

在 `src/config/env.ts` 中配置不同环境的API地址：

```typescript
// 开发环境
const devConfig = {
  apiBaseUrl: '/api', // 使用代理
  uploadUrl: '/upload'
}

// 生产环境
const prodConfig = {
  apiBaseUrl: 'https://api.yourapp.com',
  uploadUrl: 'https://api.yourapp.com/upload'
}
```

## 功能特性

### 1. 自动Token管理

请求工具会自动从localStorage或sessionStorage中获取token并添加到请求头：

```typescript
// 保存token
localStorage.setItem('token', 'your-token')

// 请求时会自动添加Authorization头
```

### 2. Loading管理

```typescript
// 显示loading（默认开启）
await request.get('/api/data')

// 不显示loading
await request.get('/api/data', {}, { showLoading: false })
```

### 3. 错误处理

```typescript
// 显示错误提示（默认开启）
await request.get('/api/data')

// 不显示错误提示
await request.get('/api/data', {}, { showError: false })
```

### 4. 文件上传

```typescript
// 上传图片
const file = document.querySelector('input[type="file"]').files[0]
const response = await api.upload.uploadImage(file)

// 上传文件
const response = await request.upload('/upload/file', file)
```

### 5. 文件下载

```typescript
// 下载文件
await request.download('/api/export', { type: 'excel' }, 'data.xlsx')
```

## API接口模块

### 用户模块 (userApi)

- `login(params)` - 用户登录
- `getUserInfo()` - 获取用户信息
- `updateUserInfo(data)` - 更新用户信息
- `logout()` - 退出登录

### 商品模块 (productApi)

- `getProductList(params)` - 获取商品列表
- `getProductDetail(id)` - 获取商品详情
- `searchProducts(keyword)` - 搜索商品

### 订单模块 (orderApi)

- `createOrder(params)` - 创建订单
- `getOrderList(page, pageSize)` - 获取订单列表
- `getOrderDetail(id)` - 获取订单详情
- `cancelOrder(id)` - 取消订单

### 上传模块 (uploadApi)

- `uploadImage(file)` - 上传图片
- `uploadFile(file)` - 上传文件

### 图表模块 (chartApi)

- `getSalesData(year)` - 获取销售数据
- `getUserStats()` - 获取用户统计
- `getCategoryStats()` - 获取分类统计

## 自定义配置

### 修改基础配置

在 `src/utlis/request.ts` 中修改：

```typescript
this.instance = axios.create({
  baseURL: '/api',
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
  },
})
```

### 添加新的API接口

在 `src/utlis/api.ts` 中添加：

```typescript
export const newApi = {
  getData(): Promise<ApiResponse<any>> {
    return request.get('/new/data')
  }
}
```

## 注意事项

1. 确保后端服务运行在 `http://localhost:8080`
2. 根据实际情况修改API接口地址和参数
3. 在生产环境中修改 `env.ts` 中的API地址
4. 可以根据UI库集成相应的Loading和Message组件

## 示例

查看 `src/utlis/example.vue` 文件获取完整的使用示例。
