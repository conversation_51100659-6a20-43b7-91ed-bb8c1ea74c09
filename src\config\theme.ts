// TDesign Mobile Vue 主题配置
export const themeConfig = {
  // 主色调
  brandColor: '#0052d9',
  
  // 成功色
  successColor: '#00a870',
  
  // 警告色
  warningColor: '#ed7b2f',
  
  // 错误色
  errorColor: '#e34d59',
  
  // 字体配置
  fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif',
  
  // 圆角配置
  borderRadius: {
    small: '4px',
    medium: '8px',
    large: '12px'
  },
  
  // 间距配置
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px'
  },
  
  // 阴影配置
  shadow: {
    light: '0 1px 4px rgba(0, 0, 0, 0.08)',
    medium: '0 2px 8px rgba(0, 0, 0, 0.12)',
    heavy: '0 4px 16px rgba(0, 0, 0, 0.16)'
  }
}

// ConfigProvider 配置
export const configProviderProps = {
  // 全局配置
  globalConfig: {
    // 类名前缀
    classPrefix: 't',
    
    // 动画配置
    animation: {
      include: ['fade', 'slide', 'zoom'],
      exclude: []
    }
  }
}

// 移动端适配配置
export const mobileConfig = {
  // 视口配置
  viewport: {
    width: 375,
    height: 667,
    devicePixelRatio: 2
  },
  
  // 安全区域配置
  safeArea: {
    top: 44,
    bottom: 34,
    left: 0,
    right: 0
  },
  
  // 触摸配置
  touch: {
    // 最小触摸目标尺寸
    minTouchTarget: 44,
    
    // 触摸反馈延迟
    touchDelay: 300
  }
}
