<template>
  <div class="tabbar-layout">
    <!-- 主内容区域 -->
    <div class="layout-main" ref="mainRef">
      <div class="layout-content">
        <slot>
          <RouterView />
        </slot>
      </div>
    </div>
    
    <!-- 底部标签栏 -->
    <t-tab-bar 
      v-model="activeTab" 
      :fixed="true"
      theme="tag" 
      :split="false"
      @change="handleTabChange"
      class="layout-tabbar"
    >
      <t-tab-bar-item v-for="item in tabList" :key="item.value" :value="item.value">
        {{ item.label }}
        <template #icon>
          <t-icon :name="item.icon" />
        </template>
      </t-tab-bar-item>
    </t-tab-bar>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Icon as TIcon } from 'tdesign-icons-vue-next'
import { RouterView } from 'vue-router'

const route = useRoute()
const router = useRouter()
const mainRef = ref<HTMLElement>()

// 底部标签栏配置
const tabList = ref([
  { value: 'home', label: '首页', icon: 'home', path: '/' },
  { value: 'category', label: '分类', icon: 'view-module', path: '/category' },
  { value: 'cart', label: '购物车', icon: 'shop', path: '/cart' },
  { value: 'profile', label: '我的', icon: 'user', path: '/profile' },
])

// 当前激活的标签
const activeTab = computed({
  get: () => {
    const path = route.path
    const tab = tabList.value.find(item => item.path === path)
    return tab?.value || 'home'
  },
  set: (value: string) => {
    handleTabChange(value)
  }
})

// 处理标签栏切换
const handleTabChange = (value: string) => {
  const tab = tabList.value.find(item => item.value === value)
  if (tab && route.path !== tab.path) {
    router.push(tab.path)
  }
}

// 处理窗口大小变化
const handleResize = () => {
  if (mainRef.value) {
    mainRef.value.style.height = 'auto'
    setTimeout(() => {
      if (mainRef.value) {
        mainRef.value.style.height = ''
      }
    }, 0)
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<script lang="ts">
export default {
  name: 'TabbarLayout'
}
</script>

<style scoped>
.tabbar-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  overflow: hidden;
}

.layout-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-bottom: 50px; /* tabbar height */
  overflow: hidden;
}

.layout-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

.layout-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
  border-top: 1px solid #e7e7e7;
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.08);
}

/* 安全区域适配 */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .layout-tabbar {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .layout-main {
    padding-bottom: calc(50px + env(safe-area-inset-bottom));
  }
}

/* 响应式适配 */
@media (max-width: 320px) {
  .layout-main {
    padding-bottom: 46px;
  }
}

@media (min-width: 768px) {
  .tabbar-layout {
    max-width: 414px;
    margin: 0 auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  }
}

/* 滚动条样式 */
.layout-content::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

/* 内容区域动画 */
.layout-content {
  transition: all 0.3s ease;
}

/* 防止内容被导航栏遮挡 */
.layout-content > * {
  position: relative;
  z-index: 1;
}
</style>
