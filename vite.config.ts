import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'
import tailwindcss from '@tailwindcss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { TDesignResolver } from 'unplugin-vue-components/resolvers'
import { babel } from '@rollup/plugin-babel';

// https://vite.dev/config/
export default defineConfig({
  plugins: [
     babel({
      plugins: [
        [
          '@babel/plugin-transform-react-jsx',
          {
            runtime: 'automatic',
            importSource: '@antv/f2',
          },
        ],
      ],
    }),
    AutoImport({
      resolvers: [
        TDesignResolver({
          library: 'mobile-vue',
        }),
      ],
    }),
    Components({
      resolvers: [
        TDesignResolver({
          library: 'mobile-vue',
        }),
      ],
    }),
    tailwindcss(),

    vue(),
    vueJsx(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    proxy: {
      '/goldApi': {
        target: 'https://gold.xianxiaobaix.top/api/open', // 后端服务地址
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/goldApi/, ''), // 移除/api前缀
      },
      // 如果需要代理其他服务，可以添加更多配置
      '/upload': {
        target: 'http://localhost:8080',
        changeOrigin: true,
      },
    },
  },
})
